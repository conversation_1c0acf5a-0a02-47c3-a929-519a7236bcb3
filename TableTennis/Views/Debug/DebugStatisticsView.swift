import SwiftUI

struct DebugStatisticsView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var selectedPlayer: Player?
    @State private var debugOutput: String = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Debug Statistics")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Use this view to debug player statistics issues")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // Player selection
                VStack(alignment: .leading, spacing: 12) {
                    Text("Select Player to Debug:")
                        .font(.headline)
                    
                    Picker("Player", selection: $selectedPlayer) {
                        Text("Select a player").tag(nil as Player?)
                        ForEach(dataManager.players, id: \.id) { player in
                            Text("\(player.name) - \(player.matchesPlayed) matches").tag(player as Player?)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // Debug buttons
                VStack(spacing: 12) {
                    HStack(spacing: 16) {
                        But<PERSON>("Debug Selected Player") {
                            if let player = selectedPlayer {
                                debugOutput = ""
                                dataManager.debugPlayerStatistics(for: player)
                            }
                        }
                        .disabled(selectedPlayer == nil)
                        .buttonStyle(.borderedProminent)

                        Button("Force Recalculate All") {
                            debugOutput = ""
                            dataManager.forceRecalculateAllStatistics()
                        }
                        .buttonStyle(.bordered)
                    }

                    Button("Recalculate All ELO Ratings") {
                        debugOutput = ""
                        dataManager.recalculateAllEloRatings()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.orange)

                    Button("Check Matches Missing ELO Changes") {
                        debugOutput = ""
                        checkMatchesMissingEloChanges()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.blue)
                }
                
                // Current statistics overview
                if !dataManager.players.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Player Statistics:")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(dataManager.players, id: \.id) { player in
                                    VStack(alignment: .leading, spacing: 2) {
                                        HStack {
                                            Text(player.name)
                                                .fontWeight(.medium)
                                            Spacer()
                                            Text("ELO: \(Int(player.eloRating))")
                                                .font(.caption)
                                                .foregroundColor(.primary)
                                                .fontWeight(.semibold)
                                        }
                                        HStack {
                                            Text("Matches: \(player.matchesPlayed)/\(player.matchesWon)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                            Spacer()
                                            Text("Games: \(player.gamesPlayed)/\(player.gamesWon)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                    .padding(.horizontal)
                                    .padding(.vertical, 4)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                }
                
                Spacer()
                
                Text("Check the Xcode console for detailed debug output")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
        }
    }

    private func checkMatchesMissingEloChanges() {
        let completedMatches = dataManager.matches.filter { $0.status == .completed }
        let matchesWithoutEloChanges = completedMatches.filter { $0.eloChanges.isEmpty }

        print("🔍 MATCHES MISSING ELO CHANGES:")
        print("📊 Total completed matches: \(completedMatches.count)")
        print("❌ Matches without ELO changes: \(matchesWithoutEloChanges.count)")

        for match in matchesWithoutEloChanges {
            let typeString = match.type == .singles ? "Singles" : (match.type == .doubles ? "Doubles" : "Mix & Match")
            let playersString = match.allPlayers.map { $0.name }.joined(separator: " vs ")
            print("   - \(typeString): \(playersString) (completed: \(match.completedAt?.formatted() ?? "unknown"))")
        }

        if matchesWithoutEloChanges.isEmpty {
            print("✅ All completed matches have ELO changes stored!")
        } else {
            print("⚠️  Run 'Migrate Matches to ELO Changes' to fix missing ELO changes")
        }
    }
}

#Preview {
    DebugStatisticsView()
        .environmentObject(DataManager())
}
